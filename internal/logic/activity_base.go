package logic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// ActivityHandler 活动处理器接口
type ActivityHandler interface {
	// GetActivityType 获取活动类型
	GetActivityType() commonPB.ACTIVITY_TYPE

	// GetSupportedEventTypes 获取支持的事件类型列表
	GetSupportedEventTypes() []commonPB.EVENT_TYPE

	// HandleEvent 处理事件
	HandleEvent(ctx context.Context, activityId int64, playerId uint64, event *commonPB.EventCommon) error

	// GetProgress 获取活动进度
	GetProgress(ctx context.Context, activityId int64, playerId uint64) (interface{}, error)

	// ClaimReward 领取奖励
	ClaimReward(ctx context.Context, activityId int64, playerId uint64, params map[string]interface{}) error
}

// ActivityManager 活动管理器
type ActivityManager struct {
	handlers map[commonPB.ACTIVITY_TYPE]ActivityHandler
	mutex    sync.RWMutex
}

// NewActivityManager 创建活动管理器
func NewActivityManager() *ActivityManager {
	return &ActivityManager{
		handlers: make(map[commonPB.ACTIVITY_TYPE]ActivityHandler),
	}
}

// RegisterHandler 注册活动处理器
func (am *ActivityManager) RegisterHandler(handler ActivityHandler) {
	am.mutex.Lock()
	defer am.mutex.Unlock()
	am.handlers[handler.GetActivityType()] = handler
}

// GetActiveActivities 获取当前开启的活动列表
func (am *ActivityManager) GetActiveActivities(ctx context.Context, eventType commonPB.EVENT_TYPE) ([]*cmodel.Activity, error) {
	entry := logx.NewLogEntry(ctx)

	// 获取所有活动配置
	activities := cmodel.GetAllActivities(consul_config.WithGrpcCtx(ctx))
	if len(activities) == 0 {
		entry.Debugf("没有找到任何活动配置")
		return nil, nil
	}

	var activeActivities []*cmodel.Activity
	now := time.Now().Unix()

	for _, activity := range activities {
		// 检查活动时间是否有效
		if now < activity.OpenAt || (activity.CloseAt > 0 && now > activity.CloseAt) {
			continue
		}

		// 检查是否有对应的处理器
		handler, exists := am.handlers[activity.ActivityType]
		if !exists {
			continue
		}

		// 检查处理器是否支持该事件类型
		supportedEvents := handler.GetSupportedEventTypes()
		eventSupported := false
		for _, supportedEvent := range supportedEvents {
			if supportedEvent == eventType {
				eventSupported = true
				break
			}
		}

		if eventSupported {
			activeActivities = append(activeActivities, activity)
		}
	}

	entry.Debugf("找到 %d 个支持事件类型 %v 的活跃活动", len(activeActivities), eventType)
	return activeActivities, nil
}

// HandleEvent 处理事件 - 分发到所有相关的活动处理器
func (am *ActivityManager) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 获取支持该事件类型的活跃活动
	activeActivities, err := am.GetActiveActivities(ctx, event.EventType)
	if err != nil {
		return fmt.Errorf("获取活跃活动失败: %w", err)
	}

	if len(activeActivities) == 0 {
		entry.Debugf("没有找到支持事件类型 %v 的活跃活动", event.EventType)
		return nil
	}

	// 遍历所有活跃活动，调用对应的处理器
	var lastError error
	successCount := 0

	for _, activity := range activeActivities {
		handler, exists := am.handlers[activity.ActivityType]
		if !exists {
			entry.Warnf("活动类型 %v 没有对应的处理器", activity.ActivityType)
			continue
		}

		err := handler.HandleEvent(ctx, activity.Id, playerId, event)
		if err != nil {
			entry.Errorf("处理活动 %d (类型: %v) 事件失败: %v", activity.Id, activity.ActivityType, err)
			lastError = err
			continue
		}

		successCount++
		entry.Debugf("成功处理活动 %d (类型: %v) 事件", activity.Id, activity.ActivityType)
	}

	entry.Infof("事件处理完成: 总活动数=%d, 成功处理=%d", len(activeActivities), successCount)

	// 如果所有活动都处理失败，返回最后一个错误
	if successCount == 0 && lastError != nil {
		return fmt.Errorf("所有活动事件处理都失败，最后错误: %w", lastError)
	}

	return nil
}

// GetProgress 获取活动进度
func (am *ActivityManager) GetProgress(ctx context.Context, activityId int64, playerId uint64) (interface{}, error) {
	// 根据活动ID获取活动配置
	activity := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activity == nil {
		return nil, fmt.Errorf("活动配置不存在: activityId=%d", activityId)
	}

	// 获取对应的处理器
	am.mutex.RLock()
	handler, exists := am.handlers[activity.ActivityType]
	am.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("活动类型 %v 没有对应的处理器", activity.ActivityType)
	}

	return handler.GetProgress(ctx, activityId, playerId)
}

// ClaimReward 领取奖励
func (am *ActivityManager) ClaimReward(ctx context.Context, activityId int64, playerId uint64, params map[string]interface{}) error {
	// 根据活动ID获取活动配置
	activity := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activity == nil {
		return fmt.Errorf("活动配置不存在: activityId=%d", activityId)
	}

	// 获取对应的处理器
	am.mutex.RLock()
	handler, exists := am.handlers[activity.ActivityType]
	am.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("活动类型 %v 没有对应的处理器", activity.ActivityType)
	}

	return handler.ClaimReward(ctx, activityId, playerId, params)
}
