package logic

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// ActivityHandler 活动处理器接口
type ActivityHandler interface {
	// GetActivityId 获取活动ID
	GetActivityId() commonPB.ACTIVITY_TYPE

	// HandleEvent 处理事件
	HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error

	// GetProgress 获取活动进度
	GetProgress(ctx context.Context, playerId uint64) (interface{}, error)

	// ClaimReward 领取奖励
	ClaimReward(ctx context.Context, playerId uint64, params map[string]interface{}) error
}
