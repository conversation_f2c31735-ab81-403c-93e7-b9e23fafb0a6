package proc

import (
	"activitysrv/internal/logic"
	"context"
	"sync"

	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// ActivityService 活动服务实现
type ActivityService struct {
}

var (
	once     sync.Once
	instance *ActivityService
)

// GetActivityServiceInstance 获取活动服务单例
func GetActivityServiceInstance() *ActivityService {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &ActivityService{}
	})
	return instance
}

// GetActivityProgress 获取活动进度
func (a *ActivityService) GetActivityProgress(ctx context.Context, req *activityPB.GetActivityProgressReq) *activityPB.GetActivityProgressRsp {
	entry := logx.NewLogEntry(ctx)

	// TODO: 从请求中获取玩家ID，暂时使用固定值
	playerId := uint64(12345)

	// 使用活动管理器获取进度
	progress, err := s.activityManager.GetProgress(ctx, int64(req.ActivityId), playerId)
	if err != nil {
		entry.Errorf("获取活动进度失败: activityId=%d, playerId=%d, err=%v", req.ActivityId, playerId, err)
		return &activityPB.GetActivityProgressRsp{
			// TODO: 设置正确的错误码
		}
	}

	// 转换为protobuf格式 (简化实现)
	entry.Debugf("获取活动进度成功: activityId=%d, playerId=%d, progress=%+v", req.ActivityId, playerId, progress)
	return &activityPB.GetActivityProgressRsp{
		// TODO: 设置正确的返回值
	}
}

// ClaimActivityReward 领取活动奖励
func (a *ActivityService) ClaimActivityReward(ctx context.Context, req *activityPB.ClaimActivityRewardReq) *activityPB.ClaimActivityRewardRsp {
	entry := logx.NewLogEntry(ctx)

	// TODO: 从请求中获取玩家ID，暂时使用固定值
	playerId := uint64(12345)

	// 构建参数map
	params := map[string]interface{}{
		"cycleId": int32(1), // TODO: 从请求中获取
		"stageId": int32(1), // TODO: 从请求中获取
	}

	// 使用活动管理器领取奖励
	err := s.activityManager.ClaimReward(ctx, int64(req.ActivityId), playerId, params)
	if err != nil {
		entry.Errorf("领取活动奖励失败: activityId=%d, playerId=%d, err=%v", req.ActivityId, playerId, err)
		return &activityPB.ClaimActivityRewardRsp{
			// TODO: 设置正确的错误码
		}
	}

	entry.Infof("领取活动奖励成功: activityId=%d, playerId=%d", req.ActivityId, playerId)
	return &activityPB.ClaimActivityRewardRsp{
		ActivityId: req.ActivityId,
		// TODO: 设置正确的返回值
	}
}
